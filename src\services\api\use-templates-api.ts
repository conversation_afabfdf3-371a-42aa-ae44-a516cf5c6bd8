import axiosInstance from 'src/utils/axios';
import { useApiServices } from 'src/services/hooks/use-api-services';

// Define the API endpoints for templates
export const templateEndpoints = {
  list: '/templates',
  details: '/templates',
};

// Define the Category interface

// Define the Template data type based on the new API response
export interface TemplateTool {
  id: number;
  templateId: number;
  toolId: number;
  createdAt: string;
  tool: {
    id: number;
    icon: string;
    name: string;
    description: string;
    createdAt: string;
    updatedAt: string;
  };
}

interface Category {
  id: number;
  name: string;
  description: string;
  icon: string;
  theme: string;
  createdAt: string;
  updatedAt: string;
}

export interface Template {
  id: number;
  creatorId: number;
  name: string;
  description: string;
  categoryId: number;
  type: string; // or more specific: "SINGLE" | "OTHER_TYPE" if you know all possible values
  model: string;
  status: string; // or more specific: "ACTIVE" | "INACTIVE" etc.
  createdAt: string;
  updatedAt: string;
  systemMessage: string;
  visibility: string; // or more specific: "PUBLIC" | "PRIVATE" etc.
  category: Category;
  templateTools: TemplateTool[];
}

export interface TemplatesResponse {
  templates: Template[];
  total: number;
}

// Define the API response structure

// Define filter parameters interface
export interface TemplateFilters {
  visibility?: 'PUBLIC' | 'PRIVATE';
  type?: string;
  category?: string;
  categoryId?: number;
  tools?: string;
  model?: string;
  status?: string;
  name?: string;
  take?: number;
  skip?: number;
}

// Create a hook to use the templates API
export const useTemplatesApi = () => {
  const apiServices = useApiServices({ axiosInstance });

  // Get all templates with optional filters
  const useGetTemplates = (filters?: TemplateFilters) => {
    return apiServices.useGetListService<TemplatesResponse, TemplateFilters>({
      url: templateEndpoints.list,
      params: filters,
    });
  };

  // Get a single template by ID
  const useGetTemplate = (id: string) => {
    return apiServices.useGetItemService<Template>({
      url: templateEndpoints.details,
      id,
    });
  };

  // Create a new template
  const useCreateTemplate = (onSuccess?: (data: any) => void) => {
    return apiServices.usePostService<Template, any>({
      url: templateEndpoints.list,
      onSuccess,
    });
  };

  // Update a template
  const useUpdateTemplate = (id: string, onSuccess?: () => void) => {
    return apiServices.usePutService<Template>({
      url: templateEndpoints.details,
      id,
      onSuccess,
    });
  };

  // Delete a template
  const useDeleteTemplate = (onSuccess?: () => void) => {
    return apiServices.useDeleteService<any>({
      url: templateEndpoints.details,
      onSuccess,
    });
  };

  return {
    useGetTemplates,
    useGetTemplate,
    useCreateTemplate,
    useUpdateTemplate,
    useDeleteTemplate,
  };
};
