import { LoadingButton } from '@mui/lab';
import { Dialog, TextField, Typography } from '@mui/material';
import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { AppButton } from 'src/components/common';
import { LoadingScreen } from 'src/components/loading-screen';
import { useSearchParams } from 'src/routes/hooks';
import { paths } from 'src/routes/paths';
import { useAuthToolsApi } from 'src/services/api/use-authtools-api';

const ConfigureTools = () => {
  const searchParams = useSearchParams();
  const state = searchParams.get('state');
  const code = searchParams.get('code');

  const navigate = useNavigate();
  useEffect(() => {
    if (state && code) {
      localStorage.setItem('state', state);
      localStorage.setItem('code', code);
    }
    const templateId = localStorage.getItem('templateId');
    navigate(paths.dashboard.agents.clone(templateId!));
  }, []);

  return <LoadingScreen />;
};

export default ConfigureTools;
