import axiosInstance from 'src/utils/axios';
import { useApiServices } from 'src/services/hooks/use-api-services';

// Define the API endpoints for Agentss
export const AgentsEndpoints = {
  list: '/tools-config',
  details: '/tools-config',
};

// Define the Category interface

// Define the API response structure

// Create a hook to use the Agentss API
export const useToolConfigApi = () => {
  const apiServices = useApiServices({ axiosInstance });

  // Get all Agentss with optional filters
  const useGetToolConfigs = (filters?: { toolId: string }) => {
    return apiServices.useGetListService<any, any>({
      url: AgentsEndpoints.list,
      params: filters,
      queryOptions: {
        enabled: filters ? (filters?.toolId?.length > 0 ? true : false) : false,
      },
    });
  };

  // Get a single Agents by ID
  const useGetConfig = (id: string) => {
    return apiServices.useGetItemService<any>({
      url: AgentsEndpoints.details,
      id,
    });
  };

  return {
    useGetToolConfigs,
    useGetConfig,
  };
};
