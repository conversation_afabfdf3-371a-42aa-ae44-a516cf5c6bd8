import { Alert, Box, Paper, Typography, Chip, Card } from '@mui/material';
import { useEffect } from 'react';
import { enqueueSnackbar } from 'notistack';
import { Iconify } from 'src/components/iconify';
import { StreamedMessage } from './use-agents-chat';

type ChatMessagesType = {
  chatMessages: StreamedMessage[];
  message?: string;
};
const ChatMessagesEvents = ({ chatMessages, message }: ChatMessagesType) => {
  const currentLng = localStorage.getItem('i18nextLng') || 'en';
  const errorMsg = chatMessages?.find((msg) => msg.event === 'error');
  useEffect(() => {
    if (errorMsg) {
      enqueueSnackbar({
        variant: 'error',
        message: errorMsg?.message,
        anchorOrigin: {
          vertical: 'top',
          horizontal: currentLng === 'en' ? 'right' : 'left',
        },
      });
    }
  }, [chatMessages]);
  return (
    <>
      {message && message?.length > 0 ? (
        <Alert sx={{ mt: '20px' }} variant="filled" color="error">
          <Typography sx={{ color: 'white' }}>{message}</Typography>
        </Alert>
      ) : (
        <></>
      )}

      {chatMessages.map((msg) => (
        <Box key={msg.id} display="flex" justifyContent="flex-start" mb={2} mt={3}>
          <Paper
            elevation={3}
            sx={{
              px: 2,
              py: 1.5,
              width: '100%',
              borderRadius: 3,

              border: '1px solid #E0DFE2',

              color: (theme) => theme.palette.text.primary,
            }}
          >
            <>
              <Typography variant="body2" fontWeight="bold" gutterBottom>
                {msg.source === 'user'
                  ? 'You'
                  : msg?.event === 'task_result'
                    ? 'Task Result'
                    : msg?.event === 'complete' || msg?.event === 'error'
                      ? ''
                      : 'Agent'}
              </Typography>
              <Typography variant="body1" whiteSpace="pre-wrap">
                {msg?.event === 'error' ? '' : msg.message}
              </Typography>
              {msg.status === 'completed' ? (
                <Chip sx={{ mt: '20px' }} variant="soft" color="success" label="Completed" />
              ) : (
                <></>
              )}
            </>
            <>
              {!errorMsg && msg?.event === 'complete' ? (
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    gap: '5px',
                    padding: '20px',
                  }}
                >
                  <Iconify
                    icon="el:ok"
                    style={{ color: '#167548', width: '24px', height: '48px' }}
                  />
                  <Typography sx={{ color: '#167548' }} variant="h6">
                    Task Completed
                  </Typography>
                </Box>
              ) : (
                <></>
              )}
            </>
            <>
              {msg?.event === 'error' ? (
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    gap: '5px',
                    padding: '20px',
                  }}
                >
                  <Iconify
                    icon="dashicons:no"
                    style={{ color: '#ff473e', width: '24px', height: '48px' }}
                  />
                  <Typography sx={{ color: '#ff473e' }} variant="h6" width="24" height="48">
                    Task Failed
                  </Typography>
                </Box>
              ) : (
                <></>
              )}
            </>
          </Paper>
        </Box>
      ))}
    </>
  );
};

export default ChatMessagesEvents;
