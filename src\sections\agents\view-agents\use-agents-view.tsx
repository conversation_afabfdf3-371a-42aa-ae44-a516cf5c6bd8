import { useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router';
import { useAgentsApi, Agent, AgentFilters } from 'src/services/api/use-agents-api';
import { useCategoriesApi } from 'src/services/api/use-categories-api';
import { useChatApi } from 'src/services/api/use-chat-api';
import { paths } from 'src/routes/paths';
import { useDebounce } from 'src/hooks/use-debounce';

// Filter options
const TYPE_FILTERS = ['All', 'SINGLE', 'TEAM'];

export const useAgentsView = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredAgents, setFilteredAgents] = useState<Agent[]>([]);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [openConfirmationDelete, setOpenConfirmationDelete] = useState(false);
  const [idAgent, setIdAgent] = useState<number | null>(null);
  const { useDeleteAgents } = useAgentsApi();
  const { useCreateChat } = useChatApi();
  const navigate = useNavigate();
  const [isCreating, setIsCreating] = useState(false);
  const { mutate: createChat, isPending: isPendingCreateChat } = useCreateChat();
  const { mutate: deleteAgent, isPending: isPendingDeleteAgent } = useDeleteAgents();

  // Debounce search query for API calls (400ms delay)
  const debouncedSearchQuery = useDebounce(searchQuery, 2000);

  // Filter states
  const [selectedTypeFilter, setSelectedTypeFilter] = useState(0);
  const [selectedCategoryFilter, setSelectedCategoryFilter] = useState(0);

  // Use the categories API hook to fetch categories (same as templates)
  const { useGetCategories } = useCategoriesApi();
  const { data: categoriesData, isLoading: categoriesLoading } = useGetCategories();

  // Create dynamic category filters with useMemo to prevent infinite re-renders (same as templates)
  const CATEGORY_FILTERS = useMemo(() => {
    if (
      !categoriesData ||
      !categoriesData.categories ||
      !Array.isArray(categoriesData.categories)
    ) {
      return ['All'];
    }
    return ['All', ...categoriesData.categories.map((category) => category.name)];
  }, [categoriesData]);

  // Build filters for API call (same logic as templates)
  const apiFilters: AgentFilters = {
    take: 15,
    skip: 0,
    // Add search query if present (backend search)
    ...(debouncedSearchQuery && { 'template-name': debouncedSearchQuery }),
    ...(selectedTypeFilter !== 0 && { type: TYPE_FILTERS[selectedTypeFilter] }),
    ...(selectedCategoryFilter !== 0 && {
      categoryId: categoriesData?.categories?.[selectedCategoryFilter - 1]?.id
        ? parseInt(categoriesData.categories[selectedCategoryFilter - 1].id, 10)
        : undefined,
    }),
  };

  // Use the agents API hook to fetch data with filters
  const { useGetAgentss } = useAgentsApi();
  const { data: agentsResponse, isLoading, error, refetch } = useGetAgentss(apiFilters);

  // Extract agents from the response
  const agents = agentsResponse?.agents || [];

  // Update filtered agents when agents data changes (no client-side filtering needed)
  useEffect(() => {
    // All filtering is now done server-side via API
    setFilteredAgents(agents);
  }, [agents]);

  // Handle search
  const handleSearch = (query: string) => {
    setSearchQuery(query);
  };
  const handleOpenAgent = async (obj: Agent) => {
    if (isCreating) return;

    setIsCreating(true);
    const body = {
      title: 'new chat',
      agentId: obj.id || '',
    };
    createChat(body, {
      onSuccess: (resChat) => {
        const newChatId = resChat?.data?.id;
        navigate(paths.dashboard.agents.chat(obj.template.id, obj.id, newChatId), {
          state: { name: obj.template.name },
        });
        setIsCreating(false);
      },
      onError: () => {
        setIsCreating(false);
      },
    });
  };
  // Handle filter changes
  const handleTypeFilterChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSelectedTypeFilter(newValue);
  };

  const handleCategoryFilterChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSelectedCategoryFilter(newValue);
  };
  const openMenu = Boolean(anchorEl);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };
  const hanldeDeleteAgent = () => {
    setOpenConfirmationDelete(true);
  };

  const handleIdAgent = (id: number) => {
    setIdAgent(id);
  };

  const handleConfirmDelete = () => {
    console.log('idAgent', idAgent);
    if (idAgent) {
      deleteAgent(idAgent, {
        onSuccess: () => {
          setOpenConfirmationDelete(false);
        },
      });
    }
  };

  return {
    // Data
    agents,
    filteredAgents,

    // Loading states
    isCreating,
    isLoading,
    categoriesLoading,
    error,
    refetch,

    // Filter state
    searchQuery,
    selectedTypeFilter,
    selectedCategoryFilter,

    // Filter options
    TYPE_FILTERS,
    CATEGORY_FILTERS,

    // Event handlers
    handleSearch,
    handleOpenAgent,
    handleTypeFilterChange,
    handleCategoryFilterChange,
    openMenu,
    handleClick,
    handleClose,
    anchorEl,
    hanldeDeleteAgent,
    openConfirmationDelete,
    setOpenConfirmationDelete,
    handleIdAgent,
    handleConfirmDelete,
    isPendingDeleteAgent,
  };
};

export type { Agent };
