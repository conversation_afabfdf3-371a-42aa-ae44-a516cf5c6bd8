import { useState } from 'react';
import {
  Box,
  Card,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  TextField,
  InputAdornment,
  Stack,
  Divider,
  Radio,
  CircularProgress,
  Chip,
} from '@mui/material';
import { Iconify } from 'src/components/iconify';
import { LoadingScreen } from 'src/components/loading-screen';

import { AppButton } from 'src/components/common/app-button';
import { paths } from 'src/routes/paths';
import { ToolConfigDialog } from './tool-config-dialog';
import useAgentCloneView from './use-agent-clone-view';
import ConfigureToolsDialog from './configure-tools-dialog';

// Mock data for tools

const AgentCloneView = () => {
  const {
    templatesResponseById,
    theme,
    toolsExpanded,
    setToolsExpanded,
    searchQuery,
    setSearchQuery,
    filteredTools,
    // getStatusColor,
    // getStatusBgColor,
    setRequirementsExpanded,
    requirementsExpanded,
    setSpecialRequest,
    specialRequest,
    handleConfigureClick,
    selectedTool,
    configDialogOpen,
    handleCloseConfigDialog,
    isLoading,
    navigate,
    isPending,
    handleContinueToTheChat,
    agentName,
    isLoadingToolsAuth,
    expandedTool,
    handleToolExpand,
    dataAuthTools,
    handleToolConfigSelect,
    selectedToolConfigs,
    dataConfigTools,
    isLoadingConfigTools,
    stateTool,
    code,
  } = useAgentCloneView();

  return (
    <>
      {/* header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Box sx={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
          <Iconify
            onClick={() => navigate(paths.dashboard.agents.templates)}
            sx={{ cursor: 'pointer' }}
            icon="fluent:ios-arrow-24-regular"
          />
          <Typography variant="h6" fontWeight={600} color="#333">
            {agentName}
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: '10px' }}>
          <Iconify icon="qlementine-icons:menu-dots-16" />
          <Iconify icon="mingcute:arrow-right-line" />
        </Box>
      </Box>
      <Divider sx={{ my: '20px' }} />

      <Typography variant="h3">Build your agent</Typography>
      <Divider sx={{ my: '20px' }} />

      {/* main card */}
      <Box>
        <Box sx={{ maxWidth: '75%', mx: 'auto' }}>
          {/* Main Card */}
          <Card
            sx={{
              p: 3,
              borderRadius: 2,
              boxShadow:
                theme.palette.mode === 'dark'
                  ? '0 4px 20px rgba(0, 0, 0, 0.3)'
                  : '0 4px 20px rgba(0, 0, 0, 0.1)',
              bgcolor: theme.palette.background.neutral,
            }}
          >
            {/* Tools & Requirements Header */}
            <Box sx={{ mb: 3 }}>
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 600,
                  color: theme.palette.text.primary,
                  mb: 0.5,
                }}
              >
                Tools & Requirements
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  color: theme.palette.text.secondary,
                }}
              >
                Set up your tool configurations
              </Typography>
            </Box>

            {/* Tools Accordion */}
            {isLoading ? (
              <LoadingScreen />
            ) : (
              <Card sx={{ padding: '24px' }}>
                <Accordion
                  expanded={toolsExpanded}
                  onChange={() => setToolsExpanded(!toolsExpanded)}
                  sx={{
                    mb: 2,
                    boxShadow: 'none',
                    // border: `1px solid ${theme.palette.divider}`,
                    borderRadius: '8px !important',

                    '&:before': {
                      display: 'none',
                    },
                  }}
                >
                  <AccordionSummary
                    expandIcon={<Iconify icon="eva:chevron-down-fill" />}
                    sx={{
                      backgroundColor:
                        theme.palette.mode === 'dark' ? theme.palette.grey[800] : 'white',
                      borderRadius: '8px',
                      '& .MuiAccordionSummary-content': {
                        margin: '12px 0',
                      },
                    }}
                  >
                    <Typography variant="subtitle1" fontWeight={600}>
                      Tools
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails sx={{ p: 3, bgcolor: theme.palette.background.neutral }}>
                    {/* Search Field */}
                    <TextField
                      fullWidth
                      placeholder="Search..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Iconify
                              icon="eva:search-fill"
                              width={20}
                              sx={{ color: theme.palette.text.disabled }}
                            />
                          </InputAdornment>
                        ),
                      }}
                      sx={{
                        mb: 3,
                        '& .MuiOutlinedInput-root': {
                          backgroundColor:
                            theme.palette.mode === 'dark' ? theme.palette.grey[900] : '#F9FAFB',
                          '& fieldset': {
                            borderColor: theme.palette.divider,
                          },
                          '&:hover fieldset': {
                            borderColor: theme.palette.primary.main,
                          },
                        },
                      }}
                    />

                    {/* Tools List as Accordions */}
                    <Stack spacing={2}>
                      {filteredTools?.map((tool) => (
                        <Accordion
                          key={tool.id}
                          expanded={expandedTool === tool.id}
                          onChange={() => handleToolExpand(tool.id)}
                          sx={{
                            border: `1px solid ${theme.palette.divider}`,
                            borderRadius: '8px !important',
                            backgroundColor: theme.palette.background.paper,
                            '&:before': {
                              display: 'none',
                            },
                          }}
                        >
                          <AccordionSummary
                            expandIcon={<Iconify icon="eva:chevron-down-fill" />}
                            sx={{
                              '& .MuiAccordionSummary-content': {
                                margin: '12px 0',
                                width: '100%',
                              },
                            }}
                          >
                            <Box
                              sx={{
                                display: 'flex',
                                justifyContent: 'space-between',
                                width: '100%',
                              }}
                            >
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                                <Iconify icon={tool?.tool?.icon} width={24} height={24} />
                                <Typography variant="body2" fontWeight={500}>
                                  {tool?.tool?.name}
                                </Typography>
                              </Box>
                              <Chip label="linked" color="success" variant="soft" />
                            </Box>
                          </AccordionSummary>
                          <AccordionDetails sx={{ pt: 0 }}>
                            {expandedTool === tool.id && (
                              <>
                                {isLoadingConfigTools ? (
                                  <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
                                    <CircularProgress size={24} />
                                  </Box>
                                ) : (
                                  <>
                                    {dataConfigTools && dataConfigTools.length > 0 ? (
                                      <Stack spacing={1}>
                                        <Typography variant="body2" fontWeight={500} sx={{ mb: 1 }}>
                                          Select a configuration:
                                        </Typography>
                                        {dataConfigTools?.map((authTool: any) => (
                                          <Box
                                            key={authTool.id}
                                            sx={{
                                              display: 'flex',
                                              alignItems: 'center',
                                              p: 1,
                                              border: `1px solid ${theme.palette.divider}`,
                                              borderRadius: 1,
                                              cursor: 'pointer',
                                              '&:hover': {
                                                backgroundColor: theme.palette.action.hover,
                                              },
                                            }}
                                            onClick={() => handleToolConfigSelect(authTool.id)}
                                          >
                                            <Radio
                                              checked={selectedToolConfigs.some(
                                                (config) => config.toolConfigId === authTool.id
                                              )}
                                              size="small"
                                            />
                                            <Box sx={{ ml: 1 }}>
                                              <Typography variant="body2">
                                                {authTool.name || `Config ${authTool.id}`}
                                              </Typography>
                                              {authTool.description && (
                                                <Typography
                                                  variant="caption"
                                                  color="text.secondary"
                                                >
                                                  {authTool.description}
                                                </Typography>
                                              )}
                                            </Box>
                                          </Box>
                                        ))}
                                      </Stack>
                                    ) : (
                                      <Box sx={{ textAlign: 'center', p: 2 }}>
                                        <Typography variant="body2" color="text.secondary">
                                          No configurations available
                                        </Typography>
                                      </Box>
                                    )}
                                  </>
                                )}
                                <AppButton
                                  label="Configure"
                                  variant="outlined"
                                  size="small"
                                  isLoading={isLoadingToolsAuth}
                                  onClick={() => handleConfigureClick(tool)}
                                  sx={{
                                    mt: 1,
                                    minWidth: 80,
                                    textTransform: 'capitalize',
                                  }}
                                />
                              </>
                            )}
                          </AccordionDetails>
                        </Accordion>
                      ))}
                    </Stack>
                  </AccordionDetails>
                </Accordion>

                {/* Requirements Accordion */}
                <Accordion
                  expanded={requirementsExpanded}
                  onChange={() => setRequirementsExpanded(!requirementsExpanded)}
                  sx={{
                    mb: 3,
                    boxShadow: 'none',

                    borderRadius: '8px !important',

                    '&:before': {
                      display: 'none',
                    },
                  }}
                >
                  <AccordionSummary
                    expandIcon={<Iconify icon="eva:chevron-down-fill" />}
                    sx={{
                      backgroundColor:
                        theme.palette.mode === 'dark' ? theme.palette.grey[800] : 'white',
                      borderRadius: '8px',
                      '& .MuiAccordionSummary-content': {
                        margin: '12px 0',
                      },
                    }}
                  >
                    <Typography variant="subtitle1" fontWeight={600}>
                      Requirements
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails sx={{ p: 3, bgcolor: theme.palette.background.neutral }}>
                    <Stack spacing={3}>
                      {/* Special Request Field */}
                      <Box>
                        <Typography
                          variant="body2"
                          sx={{
                            mb: 1,
                            fontWeight: 500,
                            color: theme.palette.text.primary,
                          }}
                        >
                          Special request
                        </Typography>
                        <TextField
                          fullWidth
                          multiline
                          rows={1}
                          placeholder="Special request"
                          value={specialRequest}
                          onChange={(e) => setSpecialRequest(e.target.value)}
                          sx={{
                            '& .MuiOutlinedInput-root': {
                              backgroundColor:
                                theme.palette.mode === 'dark' ? theme.palette.grey[900] : '#F9FAFB',
                              '& fieldset': {
                                borderColor: theme.palette.divider,
                              },
                              '&:hover fieldset': {
                                borderColor: theme.palette.primary.main,
                              },
                            },
                          }}
                        />
                      </Box>
                    </Stack>
                  </AccordionDetails>
                </Accordion>
              </Card>
            )}
          </Card>

          {/* Continue Button */}
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 4 }}>
            <AppButton
              label="Continue"
              variant="contained"
              color="primary"
              fullWidth={false}
              isLoading={isPending}
              onClick={handleContinueToTheChat}
              sx={{
                minWidth: 120,
                height: 40,
                borderRadius: 2,
                textTransform: 'capitalize',
                fontWeight: 600,
                backgroundColor: '#9C6FE4',
                '&:hover': {
                  backgroundColor: '#8B5CF6',
                },
              }}
            />
          </Box>
        </Box>
      </Box>

      {/* Configuration Dialog */}
      {selectedTool && (
        <ToolConfigDialog
          open={configDialogOpen}
          onClose={handleCloseConfigDialog}
          toolName={selectedTool?.tool.name}
          toolIcon={selectedTool?.tool?.icon || ''}
          connectedAccounts={[]}
        />
      )}

      {stateTool && code && (
        <ConfigureToolsDialog handleToolConfigSelect={handleToolConfigSelect} />
      )}
    </>
  );
};

export default AgentCloneView;
