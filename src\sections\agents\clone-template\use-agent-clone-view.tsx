import { useTheme } from '@mui/material';
import { isNumber } from '@mui/x-data-grid/internals';
import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useDebounce } from 'src/hooks/use-debounce';
import { useParams, useSearchParams } from 'src/routes/hooks';
import { paths } from 'src/routes/paths';
import { useAgentsApi } from 'src/services/api/use-agents-api';
import { useAuthToolsApi } from 'src/services/api/use-authtools-api';
import { useChatApi } from 'src/services/api/use-chat-api';
import { TemplateTool, useTemplatesApi } from 'src/services/api/use-templates-api';
import { useToolConfigApi } from 'src/services/api/use-toolconfig-api';

interface Data {
  templateId: number;
  specialRequest?: string; // Optional
  existingToolConfigs?: {
    toolConfigId: number;
  }[];

  newToolConfigs?: {
    toolId: number;
    name: string;
    config: { prompt: string; temperature: number };
  }[];
}
const useAgentCloneView = () => {
  const { id } = useParams();
  const { useGetTemplate } = useTemplatesApi();
  const { useCreateAgents } = useAgentsApi();
  const { useCreateChat } = useChatApi();

  const theme = useTheme();
  const navigate = useNavigate();
  const [authUrl, setAuthUrl] = useState<string | null>(null);
  const [toolsExpanded, setToolsExpanded] = useState(true);
  const [requirementsExpanded, setRequirementsExpanded] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [specialRequest, setSpecialRequest] = useState('');

  const [configDialogOpen, setConfigDialogOpen] = useState(false);
  const [selectedTool, setSelectedTool] = useState<TemplateTool | null>();
  const [selectToolId, setSelectedToolId] = useState<number | null>();
  const [expandedTool, setExpandedTool] = useState<number | null>(null);
  const [selectedToolConfigs, setSelectedToolConfigs] = useState<
    { toolConfigId: number; toolId: number }[]
  >([]);

  const [toolConfigSearchName, setToolConfigSearchName] = useState('');
  const depounceToolConfigSearchName = useDebounce(toolConfigSearchName, 2000);
  const { state } = useLocation();
  const agentName = state?.name || localStorage.getItem('templateName');

  const code = localStorage.getItem('code');
  const stateTool = localStorage.getItem('state');

  const { useGetAuthTools } = useAuthToolsApi();
  const { useGetToolConfigs } = useToolConfigApi();
  const { mutate: CreateAgent, isPending: isPendingCreateAgent } = useCreateAgents();

  const { mutate: createChat, isPending: isPendingCreateChat } = useCreateChat();
  const { data: templatesResponseById, isLoading } = useGetTemplate(id as string);
  if (id) {
    localStorage.setItem('templateId', id);
    localStorage.setItem('tempateName', agentName);
  }
  const {
    data: dataAuthTools,
    isLoading: isLoadingToolsAuth,
    isError,
    isSuccess,
  } = useGetAuthTools(isNumber(selectToolId) ? String(selectToolId)! : '');

  const { data: dataConfigTools, isLoading: isLoadingConfigTools } = useGetToolConfigs({
    toolId: isNumber(selectedTool?.id) ? String(selectedTool?.toolId)! : '',
    name: depounceToolConfigSearchName,
  });

  useEffect(() => {
    if (isError || isSuccess) {
      setSelectedToolId(null);
      setSelectedTool(null);
      if (dataAuthTools?.url) {
        setAuthUrl(dataAuthTools?.url);
        window.open(dataAuthTools.url, '_blank', 'noopener,noreferrer');
        setExpandedTool(-1);
      }
    }
  }, [isError, isSuccess]);
  const filteredTools =
    templatesResponseById?.templateTools?.filter((tool) =>
      tool?.tool?.name.toLowerCase().includes(searchQuery.toLowerCase())
    ) || [];

  const handleConfigureClick = (tool: TemplateTool) => {
    setSelectedToolId(tool?.toolId);
  };

  const handleCloseConfigDialog = () => {
    setConfigDialogOpen(false);
    setSelectedTool(null);
  };

  const handleToolExpand = (toolId: number) => {
    setExpandedTool(expandedTool === toolId ? null : toolId);
    // Set the selected tool when expanding to fetch auth tools
    const tool = filteredTools.find((t) => t.id === toolId);
    if (tool) {
      setSelectedTool(tool);
    }
  };
  const handleToolConfigSelect = (toolConfigId: number, toolId: number) => {
    setSelectedToolConfigs((prev) => {
      // First remove any existing config for this toolId
      const filtered = prev.filter((config) => config.toolId !== toolId);

      // Then add the new config (if toolConfigId isn't -1)
      return toolConfigId === -1
        ? filtered // -1 means deselect for this toolId
        : [...filtered, { toolConfigId, toolId }];
    });
  };

  const handleSearchQueryName = (name: string) => {
    setToolConfigSearchName(name);
  };
  // Check if a tool has at least one selected configuration
  const isToolLinked = (toolId: number) => {
    return selectedToolConfigs.some((config) => config.toolId === toolId);
  };

  // Check if all tools are linked
  const areAllToolsLinked = () => {
    return filteredTools.every((tool) => isToolLinked(tool.id));
  };

  const handleContinueToTheChat = () => {
    const existingToolConfigs = selectedToolConfigs?.map((item) => {
      return {
        toolConfigId: item?.toolConfigId,
      };
    });
    const data: Data = {
      templateId: id && id?.length > 0 ? +id : 0,
      existingToolConfigs,
      specialRequest,
    };
    if (data.specialRequest === '' || data.specialRequest == null) {
      delete data.specialRequest;
    }
    CreateAgent(data as any, {
      onSuccess: (res) => {
        const body = {
          title: 'new chat',
          agentId: res?.data?.id || '',
        };
        createChat(body, {
          onSuccess: (resChat) => {
            navigate(paths.dashboard.agents.chat(id!, res?.data?.id!, resChat?.data?.id!), {
              state: { name: templatesResponseById?.name },
            });
          },
        });
      },
    });
  };

  return {
    templatesResponseById,
    theme,
    toolsExpanded,
    setToolsExpanded,
    searchQuery,
    setSearchQuery,
    filteredTools,
    // getStatusColor,
    // getStatusBgColor,
    setRequirementsExpanded,
    requirementsExpanded,
    setSpecialRequest,
    specialRequest,
    handleConfigureClick,
    selectedTool,
    configDialogOpen,
    handleCloseConfigDialog,
    isLoading,
    navigate,
    isPending: isPendingCreateChat || isPendingCreateAgent,
    handleContinueToTheChat,
    isLoadingToolsAuth,
    agentName,
    expandedTool,
    handleToolExpand,
    dataAuthTools,
    handleToolConfigSelect,
    selectedToolConfigs,
    dataConfigTools: dataConfigTools?.toolsConfigs,
    isLoadingConfigTools,
    stateTool,
    code,
    isToolLinked,
    areAllToolsLinked,
    authUrl,
    setAuthUrl,
    depounceToolConfigSearchName,
    toolConfigSearchName,
    handleSearchQueryName,
    setToolConfigSearchName,
  };
};

export default useAgentCloneView;
