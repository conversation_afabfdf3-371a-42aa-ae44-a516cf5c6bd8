import { LoadingButton } from '@mui/lab';
import { Dialog, TextField, Typography } from '@mui/material';
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { AppButton } from 'src/components/common';
import { useSearchParams } from 'src/routes/hooks';
import { paths } from 'src/routes/paths';
import { useAuthToolsApi } from 'src/services/api/use-authtools-api';

const ConfigureToolsDialog = ({
  handleToolConfigSelect,
}: {
  handleToolConfigSelect: (tooldConfigId: number) => void;
}) => {
  const [name, setName] = useState('');
  const [openDialog, setOpenDialog] = useState(true);

  const { useCreateAuthTools } = useAuthToolsApi();

  const { mutate, isPending } = useCreateAuthTools();

  const handleAddTool = () => {
    const code = localStorage.getItem('code');
    const state = localStorage.getItem('state');
    const data = {
      code,
      state,
      name,
    };
    mutate(data, {
      onSuccess: (data) => {
        handleToolConfigSelect?.(data?.data?.id);
        setOpenDialog(false);
        localStorage.removeItem('state');
        localStorage.removeItem('code');
      },
      onError: () => {},
    });
  };

  return (
    <Dialog
      open={openDialog}
      PaperProps={{
        sx: { padding: '20px', width: '50%' },
      }}
    >
      <Typography sx={{ textAlign: 'center', my: '20px' }} variant="h6">
        provide here your tool name
      </Typography>
      <TextField
        fullWidth
        required
        placeholder="enter tool name"
        value={name}
        onChange={(e) => setName(e.target.value)}
      />
      <AppButton
        onClick={handleAddTool}
        isLoading={isPending}
        sx={{ my: '10px' }}
        label="Add"
        variant="contained"
        color="primary"
      />
    </Dialog>
  );
};

export default ConfigureToolsDialog;
